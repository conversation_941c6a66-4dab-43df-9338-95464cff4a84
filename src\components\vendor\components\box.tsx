/**
 * Enhanced Box Component
 * 
 * Wrapper around blessed.box with additional functionality
 * for layout management and styling.
 */

import blessed from 'blessed';
import { EventEmitter } from 'events';
import { toNumber } from '../../../types/blessed-extensions.js';

export interface EnhancedBoxOptions extends blessed.Widgets.BoxOptions {
  onResize?: (width: number, height: number) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onClick?: (data: any) => void;
  theme?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  padding?: number | { top?: number; right?: number; bottom?: number; left?: number };
  margin?: number | { top?: number; right?: number; bottom?: number; left?: number };
}

export class EnhancedBox extends EventEmitter {
  private box: blessed.Widgets.BoxElement;
  private options: EnhancedBoxOptions;
  private children: blessed.Widgets.Node[] = [];

  constructor(options: EnhancedBoxOptions = {}) {
    super();
    
    this.options = options;

    // Apply theme
    const themedOptions = this.applyTheme(options);

    // Create blessed box with enhanced defaults
    this.box = blessed.box({
      scrollable: false,
      alwaysScroll: false,
      mouse: true,
      keys: false,
      ...themedOptions,
    });

    this.setupEventHandlers();
    this.applySpacing();
  }

  /**
   * Apply theme styling
   */
  private applyTheme(options: EnhancedBoxOptions): blessed.Widgets.BoxOptions {
    const themes = {
      default: {
        style: {
          fg: 'white',
          bg: 'black',
          border: { fg: 'white' },
        },
      },
      primary: {
        style: {
          fg: 'white',
          bg: 'blue',
          border: { fg: 'cyan' },
        },
      },
      secondary: {
        style: {
          fg: 'black',
          bg: 'gray',
          border: { fg: 'white' },
        },
      },
      success: {
        style: {
          fg: 'white',
          bg: 'green',
          border: { fg: 'green' },
        },
      },
      warning: {
        style: {
          fg: 'black',
          bg: 'yellow',
          border: { fg: 'yellow' },
        },
      },
      error: {
        style: {
          fg: 'white',
          bg: 'red',
          border: { fg: 'red' },
        },
      },
    };

    const theme = themes[options.theme || 'default'];
    
    return {
      ...options,
      style: {
        ...theme.style,
        ...options.style,
      },
    };
  }

  /**
   * Apply padding and margin
   */
  private applySpacing(): void {
    // Apply padding (using type assertion since blessed types don't include padding)
    if (this.options.padding !== undefined) {
      if (typeof this.options.padding === 'number') {
        (this.box as any).padding = {
          top: this.options.padding,
          right: this.options.padding,
          bottom: this.options.padding,
          left: this.options.padding,
        };
      } else {
        (this.box as any).padding = {
          top: this.options.padding.top || 0,
          right: this.options.padding.right || 0,
          bottom: this.options.padding.bottom || 0,
          left: this.options.padding.left || 0,
        };
      }
    }

    // Apply margin (handled through positioning)
    if (this.options.margin !== undefined) {
      const margin = typeof this.options.margin === 'number' 
        ? { top: this.options.margin, right: this.options.margin, bottom: this.options.margin, left: this.options.margin }
        : this.options.margin;

      // Adjust position based on margin
      if (margin.top) {
        this.box.top = (this.box.top as number || 0) + margin.top;
      }
      if (margin.left) {
        this.box.left = (this.box.left as number || 0) + margin.left;
      }
      if (margin.right) {
        this.box.width = `100%-${margin.right}`;
      }
      if (margin.bottom) {
        this.box.height = `100%-${margin.bottom}`;
      }
    }
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Handle resize events
    this.box.on('resize', () => {
      const width = this.box.width as number;
      const height = this.box.height as number;
      
      if (this.options.onResize) {
        this.options.onResize(width, height);
      }
      
      this.emit('resize', width, height);
    });

    // Handle focus events
    this.box.on('focus', () => {
      if (this.options.onFocus) {
        this.options.onFocus();
      }
      
      this.emit('focus');
    });

    // Handle blur events
    this.box.on('blur', () => {
      if (this.options.onBlur) {
        this.options.onBlur();
      }
      
      this.emit('blur');
    });

    // Handle click events
    this.box.on('click', (data) => {
      if (this.options.onClick) {
        this.options.onClick(data);
      }
      
      this.emit('click', data);
    });
  }

  /**
   * Get the underlying blessed box
   */
  getBox(): blessed.Widgets.BoxElement {
    return this.box;
  }

  /**
   * Set content
   */
  setContent(content: string): void {
    this.box.setContent(content);
    this.render();
  }

  /**
   * Get content
   */
  getContent(): string {
    return this.box.getContent();
  }

  /**
   * Append content
   */
  appendContent(content: string): void {
    const currentContent = this.box.getContent();
    this.box.setContent(currentContent + content);
    this.render();
  }

  /**
   * Clear content
   */
  clear(): void {
    this.box.setContent('');
    this.render();
  }

  /**
   * Add child element
   */
  append(child: blessed.Widgets.Node): void {
    this.box.append(child);
    this.children.push(child);
    this.render();
  }

  /**
   * Remove child element
   */
  remove(child: blessed.Widgets.Node): void {
    this.box.remove(child);
    const index = this.children.indexOf(child);
    if (index !== -1) {
      this.children.splice(index, 1);
    }
    this.render();
  }

  /**
   * Insert child at index
   */
  insert(child: blessed.Widgets.Node, index: number): void {
    this.box.insert(child, index);
    this.children.splice(index, 0, child);
    this.render();
  }

  /**
   * Get all children
   */
  getChildren(): blessed.Widgets.Node[] {
    return [...this.children];
  }

  /**
   * Focus the box
   */
  focus(): void {
    this.box.focus();
  }

  /**
   * Show the box
   */
  show(): void {
    this.box.show();
    this.render();
  }

  /**
   * Hide the box
   */
  hide(): void {
    this.box.hide();
    this.render();
  }

  /**
   * Toggle visibility
   */
  toggle(): void {
    if (this.box.visible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * Set position
   */
  setPosition(options: { top?: number | string; left?: number | string; width?: number | string; height?: number | string }): void {
    if (options.top !== undefined) {this.box.top = options.top;}
    if (options.left !== undefined) {this.box.left = options.left;}
    if (options.width !== undefined) {this.box.width = options.width;}
    if (options.height !== undefined) {this.box.height = options.height;}
    this.render();
  }

  /**
   * Get position
   */
  getPosition(): { top: number; left: number; width: number; height: number } {
    return {
      top: toNumber(this.box.atop || 0),
      left: toNumber(this.box.aleft || 0),
      width: toNumber(this.box.width || 0),
      height: toNumber(this.box.height || 0),
    };
  }

  /**
   * Set style
   */
  setStyle(style: any): void {
    Object.assign(this.box.style, style);
    this.render();
  }

  /**
   * Set border
   */
  setBorder(border: blessed.Widgets.Border): void {
    this.box.border = border;
    this.render();
  }

  /**
   * Enable scrolling
   */
  enableScrolling(): void {
    (this.box as any).scrollable = true;
    (this.box as any).alwaysScroll = true;
    this.render();
  }

  /**
   * Disable scrolling
   */
  disableScrolling(): void {
    (this.box as any).scrollable = false;
    (this.box as any).alwaysScroll = false;
    this.render();
  }

  /**
   * Scroll to position
   */
  scrollTo(position: number): void {
    this.box.scrollTo(position);
    this.render();
  }

  /**
   * Scroll by amount
   */
  scroll(amount: number): void {
    this.box.scroll(amount);
    this.render();
  }

  /**
   * Get scroll position
   */
  getScrollPosition(): number {
    return this.box.getScrollHeight();
  }

  /**
   * Render the box
   */
  render(): void {
    if (this.box.screen) {
      this.box.screen.render();
    }
  }

  /**
   * Check if box is visible
   */
  get visible(): boolean {
    return this.box.visible;
  }

  /**
   * Get box dimensions
   */
  get dimensions(): { width: number; height: number } {
    return {
      width: this.box.width as number || 0,
      height: this.box.height as number || 0,
    };
  }

  /**
   * Destroy the box
   */
  destroy(): void {
    // Remove all children
    for (const child of this.children) {
      this.box.remove(child);
    }
    this.children = [];

    // Destroy the box
    this.box.destroy();
    this.removeAllListeners();
  }
}

export default EnhancedBox;
